# 毕昇系统部署配置指南

## 目录
1. [系统概述](#系统概述)
2. [环境要求](#环境要求)
3. [项目获取与部署](#项目获取与部署)
4. [关键配置修改](#关键配置修改)
5. [服务启动与管理](#服务启动与管理)
6. [系统验证](#系统验证)
7. [常见问题与解决方案](#常见问题与解决方案)
8. [默认账号密码](#默认账号密码)

## 系统概述

毕昇是一个基于大语言模型的智能对话和工作流系统，支持知识库管理、文档处理、AI对话等功能。系统采用微服务架构，包含前端、后端、数据库、向量数据库、文档服务等多个组件。

## 环境要求

### 硬件要求
- CPU: 8核以上
- 内存: 16GB以上
- 存储: 100GB以上可用空间
- 网络: 稳定的互联网连接

### 软件要求
- 操作系统: Ubuntu 20.04+ / CentOS 7+ / macOS
- Docker: 20.10+
- Docker Compose: 2.0+
- Git: 2.0+

## 项目获取与部署

### 1. 克隆项目
```bash
git clone https://github.com/dataelement/bisheng.git
cd bisheng
```

### 2. 进入部署目录
```bash
cd docker
```

### 3. 检查Docker环境
```bash
docker --version
docker-compose --version
```

## 关键配置修改

### 1. 网络配置修改

#### 修改 docker-compose.yml
需要根据你的实际IP地址修改以下配置：

**文件位置**: `docker/docker-compose.yml`

**需要修改的参数**:
```yaml
# 在 backend 服务的环境变量中
environment:
  - BISHENG_HOST=**************  # 改为你的实际IP
  
# 在 frontend 服务的环境变量中  
environment:
  - BACKEND_URL=http://**************:7861  # 改为你的实际IP
```

#### 修改前端配置
**文件位置**: `docker/nginx.conf`

如果存在nginx配置，需要修改：
```nginx
upstream backend {
    server **************:7861;  # 改为你的实际IP
}
```

### 2. 后端配置修改

#### 修改初始化配置文件
**文件位置**: `src/backend/bisheng/initdb_config.yaml`

**关键配置项**:
```yaml
env:
  # OnlyOffice文档服务地址 - 重要！
  office_url: http://**************:8701  # 改为你的实际IP
  
  # 是否开启注册
  enable_registration: true
  
  # 文件上传大小限制(MB)
  uploaded_files_maximum_size: 50
  
  # 是否显示GitHub和帮助链接
  show_github_and_help: true

# 密码安全配置
password_conf:
  # 密码有效期(天)
  password_valid_period: 200
  # 登录错误时间窗口(分钟)
  login_error_time_window: 5
  # 最大错误次数
  max_error_times: 0

# 系统登录方法
system_login_method:
  # 是否允许多点登录
  allow_multi_login: true
```

### 3. 端口配置检查

确保以下端口未被占用：
- **3001**: 前端服务
- **7861**: 后端API服务  
- **3307**: MySQL数据库
- **6380**: Redis缓存
- **9200/9300**: Elasticsearch
- **19531**: Milvus向量数据库
- **8701**: OnlyOffice文档服务
- **9100/9101**: MinIO对象存储

### 4. 嵌入模型配置修改

**文件位置**: `src/backend/bisheng/interface/embeddings/host_embedding.py`

**修改内容**:
```python
# 第133行附近，修改批处理大小
batch_size = min(batch_size, 10)  # 原来可能是更大的值，改为10
```

## 服务启动与管理

### 1. 启动所有服务
```bash
cd docker
docker-compose -f docker-compose.yml -p bisheng up -d
```

### 2. 查看服务状态
```bash
docker-compose -f docker-compose.yml -p bisheng ps
```

### 3. 查看服务日志
```bash
# 查看所有服务日志
docker-compose -f docker-compose.yml -p bisheng logs

# 查看特定服务日志
docker-compose -f docker-compose.yml -p bisheng logs backend
docker-compose -f docker-compose.yml -p bisheng logs frontend
```

### 4. 重启服务
```bash
# 重启所有服务
docker-compose -f docker-compose.yml -p bisheng restart

# 重启特定服务
docker-compose -f docker-compose.yml -p bisheng restart backend
```

### 5. 停止服务
```bash
docker-compose -f docker-compose.yml -p bisheng down
```

## 系统验证

### 1. 基础服务验证

#### 检查容器状态
```bash
docker-compose -f docker-compose.yml -p bisheng ps
```
所有服务状态应为 "Up" 或 "healthy"。

#### 检查端口监听
```bash
netstat -tlnp | grep -E "(3001|7861|3307|6380|9200|19531|8701)"
```

### 2. API服务验证

#### 后端API测试
```bash
curl -s http://localhost:7861/api/v1/env
```
正常响应示例：
```json
{
  "status_code": 200,
  "status_message": "SUCCESS", 
  "data": {
    "env": "dev",
    "office_url": "http://**************:8701",
    "version": "2.0.2"
  }
}
```

#### 前端访问测试
浏览器访问: `http://**************:3001`
应该能看到毕昇系统登录页面。

### 3. 数据库连接验证

#### MySQL连接测试
```bash
docker exec bisheng-mysql mysql -u root -p1234 -e "SHOW DATABASES;"
```

#### Redis连接测试  
```bash
docker exec bisheng-redis redis-cli ping
```
应返回 "PONG"。

### 4. 文档服务验证

#### OnlyOffice服务测试
```bash
curl -s http://localhost:8701/healthcheck
```

浏览器访问: `http://**************:8701`
应该看到OnlyOffice欢迎页面。

### 5. 功能验证

#### 登录测试
1. 访问 `http://**************:3001`
2. 使用默认账号登录：
   - 用户名: `admin`
   - 密码: `admin`

#### Word编辑器测试
1. 登录系统后，创建工作流
2. 添加"报告"节点
3. 点击Word编辑器，验证是否正常加载

#### 系统配置测试
1. 进入"系统" → "系统配置"
2. 检查 `office_url` 配置是否正确
3. 如需修改，将 `http://IP:8701` 改为 `http://**************:8701`

## 常见问题与解决方案

### 1. 端口冲突问题

**问题**: 启动时提示端口被占用
**解决方案**:
```bash
# 查找占用端口的进程
sudo lsof -i :端口号
# 或
sudo netstat -tlnp | grep 端口号

# 停止占用进程
sudo kill -9 进程ID
```

### 2. Word编辑器加载失败

**问题**: 前端显示"word编辑器加载失败"
**解决方案**:
1. 检查OnlyOffice服务状态
2. 通过前端界面修改office_url配置
3. 确保浏览器能访问OnlyOffice服务

### 3. 嵌入模型批处理错误

**问题**: 后端日志显示批处理大小错误
**解决方案**:
修改 `src/backend/bisheng/interface/embeddings/host_embedding.py` 第133行：
```python
batch_size = min(batch_size, 10)
```

### 4. 配置修改不生效

**问题**: 修改配置文件后不生效
**解决方案**:
1. 重启相关服务
2. 清除浏览器缓存
3. 检查配置文件语法是否正确

### 5. 数据库连接失败

**问题**: 后端无法连接数据库
**解决方案**:
1. 检查MySQL容器状态
2. 验证数据库密码
3. 检查网络连接

### 6. 系统完全无法访问

**问题**: 修改配置后系统完全无法使用
**解决方案**:
```bash
# 停止所有服务
docker-compose -f docker-compose.yml -p bisheng down

# 清理损坏的配置（如果需要）
docker exec bisheng-mysql mysql -u root -p1234 -e "USE bisheng; DELETE FROM config WHERE \`key\` = 'initdb_config';"

# 重新启动所有服务
docker-compose -f docker-compose.yml -p bisheng up -d
```

## 默认账号密码

### 系统登录账号
- **管理员账号**: 
  - 用户名: `admin`
  - 密码: `admin`

### 数据库账号
- **MySQL**:
  - 用户名: `root`
  - 密码: `1234`
  - 端口: `3307`
  - 数据库: `bisheng`

- **Redis**:
  - 无密码认证
  - 端口: `6380`

### 服务访问地址
- **前端系统**: `http://**************:3001`
- **后端API**: `http://**************:7861`
- **OnlyOffice**: `http://**************:8701`
- **Elasticsearch**: `http://**************:9200`
- **MinIO**: `http://**************:9100`

## 安全建议

### 1. 修改默认密码
首次部署后，建议立即修改：
- 系统管理员密码
- 数据库root密码

### 2. 网络安全
- 配置防火墙规则
- 限制不必要的端口访问
- 使用HTTPS（生产环境）

### 3. 数据备份
定期备份：
- MySQL数据库
- 上传的文件
- 系统配置

## 维护操作

### 1. 日志管理
```bash
# 查看日志大小
docker system df

# 清理日志
docker system prune -f
```

### 2. 数据备份
```bash
# 备份MySQL数据
docker exec bisheng-mysql mysqldump -u root -p1234 bisheng > backup.sql

# 恢复数据
docker exec -i bisheng-mysql mysql -u root -p1234 bisheng < backup.sql
```

### 3. 更新系统
```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose -f docker-compose.yml -p bisheng up -d --build
```

## 高级配置

### 1. 性能优化配置

#### 后端服务优化
在 `docker-compose.yml` 中调整后端服务资源：
```yaml
backend:
  deploy:
    resources:
      limits:
        memory: 4G
        cpus: '2.0'
      reservations:
        memory: 2G
        cpus: '1.0'
```

#### 数据库优化
MySQL配置优化：
```yaml
mysql:
  environment:
    - MYSQL_INNODB_BUFFER_POOL_SIZE=1G
    - MYSQL_MAX_CONNECTIONS=200
```

### 2. 生产环境配置

#### HTTPS配置
1. 准备SSL证书
2. 修改nginx配置支持HTTPS
3. 更新所有URL为HTTPS

#### 域名配置
将所有IP地址替换为域名：
```yaml
# 示例：将 ************** 替换为 bisheng.yourdomain.com
office_url: https://bisheng.yourdomain.com:8701
```

### 3. 监控配置

#### 健康检查脚本
创建 `health_check.sh`：
```bash
#!/bin/bash
echo "检查毕昇系统健康状态..."

# 检查容器状态
docker-compose -f docker-compose.yml -p bisheng ps

# 检查API响应
curl -s http://localhost:7861/api/v1/env | jq .

# 检查前端访问
curl -s -o /dev/null -w "%{http_code}" http://localhost:3001
```

## 故障排除详细指南

### 1. 启动失败排查

#### 步骤1: 检查Docker环境
```bash
# 检查Docker服务状态
sudo systemctl status docker

# 检查Docker Compose版本
docker-compose --version

# 检查磁盘空间
df -h
```

#### 步骤2: 检查端口占用
```bash
# 检查关键端口
for port in 3001 7861 3307 6380 9200 8701; do
  echo "检查端口 $port:"
  sudo lsof -i :$port
done
```

#### 步骤3: 检查配置文件
```bash
# 验证docker-compose.yml语法
docker-compose -f docker-compose.yml config

# 检查配置文件权限
ls -la docker-compose.yml
```

### 2. 服务异常排查

#### 后端服务异常
```bash
# 查看后端详细日志
docker-compose -f docker-compose.yml -p bisheng logs backend --tail=100

# 进入后端容器排查
docker exec -it bisheng-backend bash

# 检查后端配置
docker exec bisheng-backend cat /app/bisheng/initdb_config.yaml
```

#### 数据库连接异常
```bash
# 测试数据库连接
docker exec bisheng-mysql mysql -u root -p1234 -e "SELECT 1;"

# 检查数据库日志
docker-compose -f docker-compose.yml -p bisheng logs mysql

# 重置数据库（谨慎使用）
docker exec bisheng-mysql mysql -u root -p1234 -e "DROP DATABASE IF EXISTS bisheng; CREATE DATABASE bisheng CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

#### 前端访问异常
```bash
# 检查前端容器状态
docker exec bisheng-frontend nginx -t

# 查看前端日志
docker-compose -f docker-compose.yml -p bisheng logs frontend

# 检查前端配置
docker exec bisheng-frontend cat /etc/nginx/conf.d/default.conf
```

### 3. 性能问题排查

#### 系统资源监控
```bash
# 检查容器资源使用
docker stats

# 检查系统负载
top
htop

# 检查磁盘IO
iostat -x 1
```

#### 数据库性能
```bash
# 检查MySQL进程
docker exec bisheng-mysql mysql -u root -p1234 -e "SHOW PROCESSLIST;"

# 检查慢查询
docker exec bisheng-mysql mysql -u root -p1234 -e "SHOW VARIABLES LIKE 'slow_query_log';"
```

## 数据迁移指南

### 1. 数据备份
```bash
# 创建备份目录
mkdir -p backup/$(date +%Y%m%d)

# 备份MySQL数据
docker exec bisheng-mysql mysqldump -u root -p1234 --single-transaction bisheng > backup/$(date +%Y%m%d)/bisheng_db.sql

# 备份上传文件
docker cp bisheng-backend:/app/uploads backup/$(date +%Y%m%d)/uploads

# 备份配置文件
cp docker-compose.yml backup/$(date +%Y%m%d)/
cp src/backend/bisheng/initdb_config.yaml backup/$(date +%Y%m%d)/
```

### 2. 数据恢复
```bash
# 恢复数据库
docker exec -i bisheng-mysql mysql -u root -p1234 bisheng < backup/20241201/bisheng_db.sql

# 恢复上传文件
docker cp backup/20241201/uploads bisheng-backend:/app/

# 重启服务
docker-compose -f docker-compose.yml -p bisheng restart
```

## 版本升级指南

### 1. 升级前准备
```bash
# 备份当前数据
./backup.sh

# 记录当前版本
docker images | grep bisheng

# 停止服务
docker-compose -f docker-compose.yml -p bisheng down
```

### 2. 执行升级
```bash
# 拉取最新代码
git pull origin main

# 拉取最新镜像
docker-compose -f docker-compose.yml pull

# 启动新版本
docker-compose -f docker-compose.yml -p bisheng up -d
```

### 3. 升级后验证
```bash
# 检查服务状态
docker-compose -f docker-compose.yml -p bisheng ps

# 验证功能
curl -s http://localhost:7861/api/v1/env

# 检查版本信息
docker exec bisheng-backend cat /app/version.txt
```

## 开发环境配置

### 1. 本地开发设置
```bash
# 克隆项目
git clone https://github.com/dataelement/bisheng.git
cd bisheng

# 安装Python依赖
cd src/backend
pip install -r requirements.txt

# 安装前端依赖
cd ../frontend
npm install
```

### 2. 调试配置
```bash
# 启动后端调试模式
cd src/backend
python -m uvicorn bisheng.main:app --reload --host 0.0.0.0 --port 7860

# 启动前端开发服务器
cd src/frontend
npm run dev
```

---

**注意**: 本文档基于毕昇v2.0.2版本编写，不同版本可能存在差异。部署前请确认版本兼容性。

**技术支持**: 如遇到问题，请查看官方文档或提交Issue到GitHub仓库。

**文档版本**: v1.0 (2024-12-01)
**适用版本**: 毕昇 v2.0.2+
