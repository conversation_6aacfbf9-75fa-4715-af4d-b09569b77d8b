# 毕昇系统快速参考卡片

## 🚀 快速启动

```bash
# 进入项目目录
cd bisheng/docker

# 启动所有服务
docker-compose -f docker-compose.yml -p bisheng up -d

# 查看服务状态
docker-compose -f docker-compose.yml -p bisheng ps
```

## 🔧 关键配置文件

| 文件 | 用途 | 关键配置 |
|------|------|----------|
| `docker/docker-compose.yml` | 服务编排 | IP地址、端口映射 |
| `src/backend/bisheng/initdb_config.yaml` | 系统配置 | office_url、安全设置 |
| `src/backend/bisheng/interface/embeddings/host_embedding.py` | 嵌入模型 | 批处理大小 |

## 🌐 默认访问地址

| 服务 | 地址 | 用途 |
|------|------|------|
| 前端系统 | http://**************:3001 | 主要界面 |
| 后端API | http://**************:7861 | API服务 |
| OnlyOffice | http://**************:8701 | 文档编辑 |
| MySQL | **************:3307 | 数据库 |
| Redis | **************:6380 | 缓存 |

## 🔑 默认账号密码

| 服务 | 用户名 | 密码 |
|------|--------|------|
| 系统管理员 | admin | admin |
| MySQL | root | 1234 |
| Redis | - | 无密码 |

## ⚡ 常用命令

### 服务管理
```bash
# 启动服务
docker-compose -f docker-compose.yml -p bisheng up -d

# 停止服务
docker-compose -f docker-compose.yml -p bisheng down

# 重启服务
docker-compose -f docker-compose.yml -p bisheng restart

# 查看日志
docker-compose -f docker-compose.yml -p bisheng logs backend
```

### 健康检查
```bash
# API健康检查
curl -s http://localhost:7861/api/v1/env

# 数据库连接测试
docker exec bisheng-mysql mysql -u root -p1234 -e "SELECT 1;"

# Redis连接测试
docker exec bisheng-redis redis-cli ping
```

### 故障排查
```bash
# 查看容器状态
docker ps | grep bisheng

# 查看资源使用
docker stats

# 检查端口占用
sudo lsof -i :3001
sudo lsof -i :7861
```

## 🛠️ 必须修改的配置

### 1. IP地址配置
将所有 `**************` 替换为你的实际IP地址：

**docker-compose.yml**:
```yaml
environment:
  - BISHENG_HOST=你的IP地址
  - BACKEND_URL=http://你的IP地址:7861
```

**initdb_config.yaml**:
```yaml
env:
  office_url: http://你的IP地址:8701
```

### 2. 嵌入模型批处理
**host_embedding.py** (第133行):
```python
batch_size = min(batch_size, 10)
```

## 🚨 常见问题快速解决

### Word编辑器加载失败
1. 检查OnlyOffice服务: `docker ps | grep office`
2. 前端修改office_url: 系统→系统配置
3. 将 `http://IP:8701` 改为 `http://你的IP:8701`

### 端口冲突
```bash
# 查找占用进程
sudo lsof -i :端口号
# 停止进程
sudo kill -9 进程ID
```

### 系统无法访问
```bash
# 完全重启
docker-compose -f docker-compose.yml -p bisheng down
docker-compose -f docker-compose.yml -p bisheng up -d
```

### 配置损坏恢复
```bash
# 清理损坏配置
docker exec bisheng-mysql mysql -u root -p1234 -e "USE bisheng; DELETE FROM config WHERE \`key\` = 'initdb_config';"
# 重启服务
docker-compose -f docker-compose.yml -p bisheng restart backend
```

## 📋 部署检查清单

- [ ] Docker和Docker Compose已安装
- [ ] 所需端口未被占用
- [ ] IP地址配置已修改
- [ ] 嵌入模型批处理已修改
- [ ] 服务启动成功
- [ ] API响应正常
- [ ] 前端可访问
- [ ] 登录功能正常
- [ ] Word编辑器正常

## 🔍 验证命令

```bash
# 一键验证脚本
echo "=== 毕昇系统状态检查 ==="
echo "1. 容器状态:"
docker-compose -f docker-compose.yml -p bisheng ps

echo -e "\n2. API测试:"
curl -s http://localhost:7861/api/v1/env | jq .status_code

echo -e "\n3. 前端测试:"
curl -s -o /dev/null -w "HTTP状态码: %{http_code}\n" http://localhost:3001

echo -e "\n4. 数据库测试:"
docker exec bisheng-mysql mysql -u root -p1234 -e "SELECT 'MySQL连接正常' as status;"

echo -e "\n5. Redis测试:"
docker exec bisheng-redis redis-cli ping
```

## 📞 技术支持

- **官方文档**: https://github.com/dataelement/bisheng
- **问题反馈**: GitHub Issues
- **社区讨论**: 官方论坛

---
**快速参考版本**: v1.0 | **更新日期**: 2024-12-01
