#!/bin/bash

# 毕昇系统自动部署脚本
# 版本: v1.0
# 作者: AI Assistant
# 日期: 2024-12-01

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 获取本机IP地址
get_local_ip() {
    # 尝试多种方法获取IP
    local ip=""
    
    # 方法1: 通过默认路由
    ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    
    # 方法2: 通过hostname
    if [ -z "$ip" ]; then
        ip=$(hostname -I | awk '{print $1}')
    fi
    
    # 方法3: 通过ifconfig
    if [ -z "$ip" ]; then
        ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
    fi
    
    echo "$ip"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用"
        lsof -Pi :$port -sTCP:LISTEN
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        log_success "端口 $port 可用"
    fi
}

# 备份原始文件
backup_file() {
    local file=$1
    if [ -f "$file" ]; then
        cp "$file" "$file.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "已备份 $file"
    fi
}

# 替换文件中的IP地址
replace_ip_in_file() {
    local file=$1
    local old_ip=$2
    local new_ip=$3
    
    if [ -f "$file" ]; then
        backup_file "$file"
        sed -i "s/$old_ip/$new_ip/g" "$file"
        log_success "已更新 $file 中的IP地址: $old_ip -> $new_ip"
    else
        log_warning "文件不存在: $file"
    fi
}

# 修改嵌入模型配置
fix_embedding_config() {
    local file="src/backend/bisheng/interface/embeddings/host_embedding.py"
    if [ -f "$file" ]; then
        backup_file "$file"
        # 查找并替换批处理大小配置
        sed -i 's/batch_size = min(batch_size, [0-9]\+)/batch_size = min(batch_size, 10)/g' "$file"
        log_success "已修复嵌入模型批处理配置"
    else
        log_warning "嵌入模型配置文件不存在: $file"
    fi
}

# 主函数
main() {
    log_info "开始毕昇系统部署..."
    
    # 检查必要的命令
    log_info "检查系统环境..."
    check_command "docker"
    check_command "docker-compose"
    check_command "git"
    
    # 获取IP地址
    local_ip=$(get_local_ip)
    if [ -z "$local_ip" ]; then
        log_error "无法获取本机IP地址"
        exit 1
    fi
    
    log_info "检测到本机IP地址: $local_ip"
    read -p "是否使用此IP地址？(Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        read -p "请输入要使用的IP地址: " local_ip
    fi
    
    log_info "将使用IP地址: $local_ip"
    
    # 检查关键端口
    log_info "检查端口占用情况..."
    ports=(3001 7861 3307 6380 9200 8701 19531 9100)
    for port in "${ports[@]}"; do
        check_port $port
    done
    
    # 检查项目目录
    if [ ! -f "docker/docker-compose.yml" ]; then
        log_error "请在毕昇项目根目录下运行此脚本"
        exit 1
    fi
    
    # 修改配置文件
    log_info "修改配置文件..."
    
    # 修改docker-compose.yml
    replace_ip_in_file "docker/docker-compose.yml" "**************" "$local_ip"
    
    # 修改初始化配置
    replace_ip_in_file "src/backend/bisheng/initdb_config.yaml" "**************" "$local_ip"
    
    # 修复嵌入模型配置
    fix_embedding_config
    
    # 进入docker目录
    cd docker
    
    # 停止可能存在的服务
    log_info "停止现有服务..."
    docker-compose -f docker-compose.yml -p bisheng down 2>/dev/null || true
    
    # 启动服务
    log_info "启动毕昇服务..."
    docker-compose -f docker-compose.yml -p bisheng up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    log_info "检查服务状态..."
    docker-compose -f docker-compose.yml -p bisheng ps
    
    # 验证服务
    log_info "验证服务..."
    
    # 检查API
    if curl -s "http://localhost:7861/api/v1/env" >/dev/null 2>&1; then
        log_success "后端API服务正常"
    else
        log_warning "后端API服务可能未就绪，请稍后检查"
    fi
    
    # 检查前端
    if curl -s -o /dev/null -w "%{http_code}" "http://localhost:3001" | grep -q "200\|302\|301"; then
        log_success "前端服务正常"
    else
        log_warning "前端服务可能未就绪，请稍后检查"
    fi
    
    # 显示访问信息
    echo
    log_success "毕昇系统部署完成！"
    echo
    echo "访问信息:"
    echo "  前端地址: http://$local_ip:3001"
    echo "  后端API: http://$local_ip:7861"
    echo "  OnlyOffice: http://$local_ip:8701"
    echo
    echo "默认登录信息:"
    echo "  用户名: admin"
    echo "  密码: admin"
    echo
    echo "数据库信息:"
    echo "  MySQL: $local_ip:3307 (root/1234)"
    echo "  Redis: $local_ip:6380"
    echo
    log_info "请等待1-2分钟让所有服务完全启动"
    log_info "如果Word编辑器无法使用，请登录系统后在'系统→系统配置'中修改office_url"
}

# 显示帮助信息
show_help() {
    echo "毕昇系统自动部署脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --check    仅检查环境，不执行部署"
    echo "  -s, --status   检查服务状态"
    echo "  -r, --restart  重启服务"
    echo "  -d, --down     停止服务"
    echo
    echo "示例:"
    echo "  $0              # 执行完整部署"
    echo "  $0 --check     # 仅检查环境"
    echo "  $0 --status    # 检查服务状态"
}

# 检查服务状态
check_status() {
    log_info "检查毕昇服务状态..."
    
    if [ -f "docker/docker-compose.yml" ]; then
        cd docker
        docker-compose -f docker-compose.yml -p bisheng ps
        
        echo
        log_info "API健康检查:"
        curl -s http://localhost:7861/api/v1/env | jq . 2>/dev/null || echo "API未响应或jq未安装"
        
        echo
        log_info "前端访问检查:"
        http_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001)
        echo "HTTP状态码: $http_code"
    else
        log_error "请在毕昇项目根目录下运行此脚本"
    fi
}

# 重启服务
restart_services() {
    log_info "重启毕昇服务..."
    
    if [ -f "docker/docker-compose.yml" ]; then
        cd docker
        docker-compose -f docker-compose.yml -p bisheng restart
        log_success "服务重启完成"
    else
        log_error "请在毕昇项目根目录下运行此脚本"
    fi
}

# 停止服务
stop_services() {
    log_info "停止毕昇服务..."
    
    if [ -f "docker/docker-compose.yml" ]; then
        cd docker
        docker-compose -f docker-compose.yml -p bisheng down
        log_success "服务已停止"
    else
        log_error "请在毕昇项目根目录下运行此脚本"
    fi
}

# 仅检查环境
check_only() {
    log_info "检查部署环境..."
    
    # 检查命令
    check_command "docker"
    check_command "docker-compose"
    check_command "git"
    
    # 检查IP
    local_ip=$(get_local_ip)
    log_info "本机IP地址: $local_ip"
    
    # 检查端口
    ports=(3001 7861 3307 6380 9200 8701 19531 9100)
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            log_warning "端口 $port 已被占用"
        else
            log_success "端口 $port 可用"
        fi
    done
    
    # 检查项目文件
    if [ -f "docker/docker-compose.yml" ]; then
        log_success "找到docker-compose.yml"
    else
        log_error "未找到docker-compose.yml，请确认在项目根目录"
    fi
    
    if [ -f "src/backend/bisheng/initdb_config.yaml" ]; then
        log_success "找到initdb_config.yaml"
    else
        log_warning "未找到initdb_config.yaml"
    fi
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -c|--check)
        check_only
        exit 0
        ;;
    -s|--status)
        check_status
        exit 0
        ;;
    -r|--restart)
        restart_services
        exit 0
        ;;
    -d|--down)
        stop_services
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
